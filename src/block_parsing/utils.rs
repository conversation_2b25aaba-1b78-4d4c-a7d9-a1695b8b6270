use regex::Regex;

#[derive(<PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON>ialEq, Eq)]
pub struct RawCodeBlock {
    pub keyword: String,
    pub content_after_keyword: String,
    pub full_block_text: String,
}

// New struct to hold both closed and a potential unclosed block at the end
#[derive(<PERSON><PERSON><PERSON>, <PERSON>lone, PartialEq, Eq)]
pub struct ExtractedBlocks {
    pub closed_blocks: Vec<RawCodeBlock>,
    pub unclosed_block_at_end: Option<RawCodeBlock>,
}

/// Extracts all raw code blocks from the response text.
/// Identifies fully closed blocks and a potential unclosed block at the end.
/// A raw code block is defined as ```keyword ... ``` or ``` ... ```.
/// The keyword is the first word after ``` (if present and not containing newlines).
/// The content_after_keyword is everything else within the backticks.
pub fn extract_raw_code_blocks(response_text: &str) -> ExtractedBlocks {
    // Regex to find ```keyword content``` or ```content``` blocks.
    // (?m) enables multiline mode, where ^ and $ match the start/end of a line.
    // ^\s*: Matches optional leading whitespace on the line where the block starts.
    // (```...```): Captures the entire block content (Group 1 - full_block_text).
    //   ```: Matches the opening backticks.
    //   \s*?: Non-greedy match for optional whitespace immediately after ```.
    //   ([a-zA-Z0-9_-]*): Captures the keyword (Group 2). Allows alphanumeric, underscore, hyphen. Empty if no keyword.
    //   (\s*[\s\S]*?): Non-greedily captures the content after the keyword (Group 3),
    //                  including leading whitespace/newline and all subsequent content until closing ```.
    //   ```: Matches the closing backticks.
    // \s*$: Matches optional trailing whitespace on the line where the block ends.
    // MODIFIED: Removed \s*$ from the end of the regex to allow arbitrary text after the closing ```
    // Regex for closed blocks
    // This regex assumes a multi-line block structure for robustness with internal ```:
    // - Starts with ``` optional_keyword on a line.
    // - Followed by a newline.
    // - Content.
    // - Ends with a newline then ``` on a line.
    // Group 1: Full block text.
    // Group 2: Keyword.
    // Group 3: Content part.
    // The content group `([\s\S]*?)` is a non-greedy match for any character (including newlines)
    // between the keyword line and the closing backticks line.
    let closed_block_re =
        Regex::new(r"(?m)^(\s*```\s*([a-zA-Z0-9_-]*)?\s*\n([\s\S]*?)\n\s*```\s*)$") // Non-greedy content
            .expect("Failed to compile closed code block regex");

    // Regex for potentially unclosed blocks (matches from ```keyword to end of text if not closed)
    // This is used on the remainder of the text after all closed blocks are processed.
    // Group 1: Full unclosed block text (e.g., ```keyword\ncontent)
    // Group 2: Content after ``` (e.g., keyword\ncontent or \ncontent)
    let unclosed_block_re =
        Regex::new(r"(?m)(^\s*```([\s\S]*))").expect("Failed to compile unclosed code block regex");

    let mut closed_blocks = Vec::new();
    let mut last_match_end = 0;

    for cap in closed_block_re.captures_iter(response_text) {
        let full_block_match = cap.get(1).unwrap(); // The full ```...``` block
        let full_block_text = full_block_match.as_str().to_string();
        last_match_end = full_block_match.end();

        let keyword = cap.get(2).map_or("", |m| m.as_str()).to_string();
        let content_after_keyword = cap.get(3).map_or("", |m| m.as_str()).to_string();

        closed_blocks.push(RawCodeBlock {
            keyword,
            content_after_keyword,
            full_block_text,
        });
    }

    let mut unclosed_block_at_end: Option<RawCodeBlock> = None;
    if last_match_end < response_text.len() {
        let remaining_text = &response_text[last_match_end..];
        if let Some(cap) = unclosed_block_re.captures(remaining_text) {
            // Check if this match itself contains a closing ```. If so, it's likely a malformed closed block, not an unclosed one.
            // This simple check helps avoid misidentifying blocks like "```keyword\ncontent```extra" where "extra" is not a new block.
            // A more robust solution might involve checking if the `full_block_text` from this capture,
            // when taken from the original `response_text`, actually ends at EOF.
            let potential_unclosed_full_text_trimmed = cap.get(1).unwrap().as_str().trim_end(); // cap.get(1) is the full block text
                                                                                                // If the trimmed full text is just "```" or it doesn't end with "```" at all, it's unclosed.
                                                                                                // This handles cases like "```" or "```keyword" or "```keyword\ncontent"
            if potential_unclosed_full_text_trimmed == "```"
                || !potential_unclosed_full_text_trimmed.ends_with("```")
            {
                let full_block_text_unclosed = cap.get(1).unwrap().as_str().to_string();
                let text_after_opening_ticks = cap.get(2).unwrap().as_str();

                let (keyword, content_after_keyword) =
                    parse_keyword_and_content_for_unclosed(text_after_opening_ticks);

                unclosed_block_at_end = Some(RawCodeBlock {
                    keyword,
                    content_after_keyword,
                    full_block_text: full_block_text_unclosed,
                });
            }
        }
    }

    ExtractedBlocks {
        closed_blocks,
        unclosed_block_at_end,
    }
}

// Helper function to parse keyword and content from the text following opening ``` of an unclosed block.
fn parse_keyword_and_content_for_unclosed(text_after_opening_ticks: &str) -> (String, String) {
    let mut keyword = "".to_string();
    let mut content_after_keyword = text_after_opening_ticks.to_string(); // Default to the whole string

    if let Some(newline_idx) = text_after_opening_ticks.find('\n') {
        let first_line = text_after_opening_ticks[..newline_idx].trim();
        // Content after the first newline character
        let rest_of_text = &text_after_opening_ticks[newline_idx + 1..];

        if !first_line.is_empty() {
            if Regex::new(r"^([a-zA-Z0-9_-]+)$")
                .unwrap()
                .is_match(first_line)
            {
                // First line is purely a keyword
                keyword = first_line.to_string();
                content_after_keyword = rest_of_text.to_string();
            } else if let Some(fcaps) = Regex::new(r"^([a-zA-Z0-9_-]+)\s+(.*)$")
                .unwrap()
                .captures(first_line)
            {
                // First line is "keyword content_on_same_line"
                keyword = fcaps.get(1).unwrap().as_str().to_string();
                let content_on_first_line = fcaps.get(2).unwrap().as_str();
                content_after_keyword = format!("{}\n{}", content_on_first_line, rest_of_text);
            } else {
                // First line is not a keyword or "keyword content", so it's part of the content.
                // Keyword remains empty, content_after_keyword is the original text_after_opening_ticks.
                // This case is already handled by the initial value of content_after_keyword.
            }
        } else {
            // First line (before newline) is empty or whitespace (e.g. ``` \n content or ```\n content)
            // Keyword is empty. The content is the entire text_after_opening_ticks.
            // This ensures that if text_after_opening_ticks was e.g. "\nfoo", content_after_keyword becomes "\nfoo".
            content_after_keyword = text_after_opening_ticks.to_string();
        }
    } else {
        // text_after_opening_ticks is a single line (no newlines)
        let trimmed_line = text_after_opening_ticks.trim();
        if !trimmed_line.is_empty() {
            if Regex::new(r"^([a-zA-Z0-9_-]+)$")
                .unwrap()
                .is_match(trimmed_line)
            {
                // Single line is purely a keyword
                keyword = trimmed_line.to_string();
                content_after_keyword = "".to_string();
            } else if let Some(fcaps) = Regex::new(r"^([a-zA-Z0-9_-]+)\s+(.*)$")
                .unwrap()
                .captures(trimmed_line)
            {
                // Single line is "keyword content"
                keyword = fcaps.get(1).unwrap().as_str().to_string();
                content_after_keyword = fcaps.get(2).unwrap().as_str().to_string();
            } else {
                // Single line is content without a parsable keyword
                // Keyword remains empty, content_after_keyword is the original text_after_opening_ticks.
                // This case is already handled by the initial value of content_after_keyword.
                // However, we might want to trim leading whitespace from content if no keyword.
                content_after_keyword = text_after_opening_ticks.trim_start().to_string();
            }
        } else {
            // text_after_opening_ticks is empty or all whitespace
            keyword = "".to_string();
            content_after_keyword = "".to_string();
        }
    }
    (keyword, content_after_keyword)
}

/// Strips content within <think>...</think> tags from the input text.
/// This is used to remove LLM's internal "thinking" process from the response
/// before attempting to parse structured blocks.
pub(crate) fn strip_think_tags(text: &str) -> String {
    // Regex to find <think>...</think> blocks.
    // <think>: Matches the opening tag.
    // [\s\S]*?: Non-greedily matches any character (including newlines) zero or more times.
    // </think>: Matches the closing tag.
    let think_block_re =
        Regex::new(r"<think>[\s\S]*?</think>").expect("Failed to compile think_block regex");
    think_block_re.replace_all(text, "").into_owned()
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_extract_simple_block_with_keyword() {
        let text = "```rust\nlet x = 1;\n```";
        let extracted = extract_raw_code_blocks(text);
        assert_eq!(extracted.closed_blocks.len(), 1);
        assert_eq!(extracted.closed_blocks[0].keyword, "rust");
        assert_eq!(
            extracted.closed_blocks[0].content_after_keyword,
            "let x = 1;"
        ); // Adjusted
        assert_eq!(
            extracted.closed_blocks[0].full_block_text,
            "```rust\nlet x = 1;\n```"
        );
        assert!(extracted.unclosed_block_at_end.is_none());
    }

    #[test]
    fn test_extract_simple_block_no_keyword() {
        let text = "```\nlet x = 1;\n```";
        let extracted = extract_raw_code_blocks(text);
        assert_eq!(extracted.closed_blocks.len(), 1);
        assert_eq!(extracted.closed_blocks[0].keyword, "");
        assert_eq!(
            extracted.closed_blocks[0].content_after_keyword,
            "let x = 1;"
        ); // Adjusted
        assert_eq!(
            extracted.closed_blocks[0].full_block_text,
            "```\nlet x = 1;\n```"
        );
        assert!(extracted.unclosed_block_at_end.is_none());
    }

    #[test]
    fn test_extract_block_keyword_no_space_content() {
        let text = "```rustlet x = 1;```"; // Single-line block
        let extracted = extract_raw_code_blocks(text);
        assert_eq!(extracted.closed_blocks.len(), 0); // Will not match strict closed_block_re
        assert!(extracted.unclosed_block_at_end.is_none()); // Structurally complete single-line block, not "unclosed"
    }

    #[test]
    fn test_extract_block_keyword_with_space_then_content() {
        let text = "```python print('hello')```"; // Single-line block
        let extracted = extract_raw_code_blocks(text);
        assert_eq!(extracted.closed_blocks.len(), 0); // Will not match strict closed_block_re
        assert!(extracted.unclosed_block_at_end.is_none()); // Structurally complete single-line block, not "unclosed"
    }

    #[test]
    fn test_extract_block_keyword_with_newline_then_content() {
        let text = "```json\n{\"key\": \"value\"}\n```";
        let extracted = extract_raw_code_blocks(text);
        assert_eq!(extracted.closed_blocks.len(), 1);
        assert_eq!(extracted.closed_blocks[0].keyword, "json");
        assert_eq!(
            extracted.closed_blocks[0].content_after_keyword,
            "{\"key\": \"value\"}"
        ); // Adjusted
        assert_eq!(
            extracted.closed_blocks[0].full_block_text,
            "```json\n{\"key\": \"value\"}\n```"
        );
        assert!(extracted.unclosed_block_at_end.is_none());
    }

    #[test]
    fn test_extract_multiple_blocks() {
        let text = "Text before\n```range\npath/to/file.rs\n1-5\n```\nSome other text\n```replace\nnew content\n```\nText after";
        let extracted = extract_raw_code_blocks(text);
        assert_eq!(extracted.closed_blocks.len(), 2);

        assert_eq!(extracted.closed_blocks[0].keyword, "range");
        assert_eq!(
            extracted.closed_blocks[0].content_after_keyword,
            "path/to/file.rs\n1-5"
        ); // Adjusted
        assert_eq!(
            extracted.closed_blocks[0].full_block_text,
            "```range\npath/to/file.rs\n1-5\n```"
        );

        assert_eq!(extracted.closed_blocks[1].keyword, "replace");
        assert_eq!(
            extracted.closed_blocks[1].content_after_keyword,
            "new content"
        ); // Adjusted
        assert_eq!(
            extracted.closed_blocks[1].full_block_text,
            "```replace\nnew content\n```"
        );
        assert!(extracted.unclosed_block_at_end.is_none());
    }

    #[test]
    fn test_extract_block_no_keyword_multiline_content() {
        let text = "```\nLine 1\nLine 2\n```";
        let extracted = extract_raw_code_blocks(text);
        assert_eq!(extracted.closed_blocks.len(), 1);
        assert_eq!(extracted.closed_blocks[0].keyword, "");
        assert_eq!(
            extracted.closed_blocks[0].content_after_keyword,
            "Line 1\nLine 2"
        ); // Adjusted
        assert_eq!(
            extracted.closed_blocks[0].full_block_text,
            "```\nLine 1\nLine 2\n```"
        );
        assert!(extracted.unclosed_block_at_end.is_none());
    }

    #[test]
    fn test_extract_empty_block_with_keyword() {
        let text = "```rust```"; // Single-line block
        let extracted = extract_raw_code_blocks(text);
        assert_eq!(extracted.closed_blocks.len(), 0); // Will not match strict closed_block_re
        assert!(extracted.unclosed_block_at_end.is_none()); // Structurally complete single-line block, not "unclosed"
    }

    #[test]
    fn test_extract_empty_block_no_keyword() {
        let text = "```\n\n```"; // Two newlines between ``` and ```
        let extracted = extract_raw_code_blocks(text);
        assert_eq!(extracted.closed_blocks.len(), 1);
        assert_eq!(extracted.closed_blocks[0].keyword, "");
        assert_eq!(extracted.closed_blocks[0].content_after_keyword, ""); // Content between newlines is empty
        assert_eq!(extracted.closed_blocks[0].full_block_text, "```\n\n```");
        assert!(extracted.unclosed_block_at_end.is_none());
    }

    #[test]
    fn test_extract_block_fully_empty() {
        let text = "``````"; // Single-line block, no keyword, no content
        let extracted = extract_raw_code_blocks(text);
        assert_eq!(extracted.closed_blocks.len(), 0); // Will not match strict closed_block_re
        assert!(extracted.unclosed_block_at_end.is_none()); // Structurally complete single-line block, not "unclosed"
    }

    #[test]
    fn test_no_blocks() {
        let text = "Just some text, no blocks here.";
        let extracted = extract_raw_code_blocks(text);
        assert!(extracted.closed_blocks.is_empty());
        assert!(extracted.unclosed_block_at_end.is_none());
    }

    #[test]
    fn test_block_with_leading_and_trailing_whitespace_around_backticks() {
        let text = "  ```  keyword  \ncontent\n  ```  "; // Spaces around backticks and keyword
        let extracted = extract_raw_code_blocks(text);
        assert_eq!(extracted.closed_blocks.len(), 1);
        assert_eq!(extracted.closed_blocks[0].keyword, "keyword");
        assert_eq!(
            extracted.closed_blocks[0].content_after_keyword,
            "content" // Content between newlines, surrounding spaces on ``` lines are ignored by main regex
        ); // Adjusted
        assert_eq!(
            extracted.closed_blocks[0].full_block_text,
            "  ```  keyword  \ncontent\n  ```  "
        );
        assert!(extracted.unclosed_block_at_end.is_none());
    }

    #[test]
    fn test_keyword_with_hyphen_and_underscore() {
        let text = "```my-keyword_test\ncontent\n```";
        let extracted = extract_raw_code_blocks(text);
        assert_eq!(extracted.closed_blocks.len(), 1);
        assert_eq!(extracted.closed_blocks[0].keyword, "my-keyword_test");
        assert_eq!(extracted.closed_blocks[0].content_after_keyword, "content"); // Adjusted
        assert!(extracted.unclosed_block_at_end.is_none());
    }

    #[test]
    fn test_extract_block_keyword_newline_then_empty_content_then_newline_closing_ticks() {
        let text = "```keyword\n\n```"; // Keyword, newline, empty line (content), newline, closing ticks
        let extracted = extract_raw_code_blocks(text);
        assert_eq!(extracted.closed_blocks.len(), 1);
        assert_eq!(extracted.closed_blocks[0].keyword, "keyword");
        assert_eq!(extracted.closed_blocks[0].content_after_keyword, ""); // Content between newlines is empty
        assert_eq!(
            extracted.closed_blocks[0].full_block_text,
            "```keyword\n\n```"
        );
        assert!(extracted.unclosed_block_at_end.is_none());
    }

    #[test]
    fn test_extract_block_keyword_newline_then_closing_ticks() {
        let text = "```keyword\n```"; // Keyword, newline, then immediately closing ticks
        let extracted = extract_raw_code_blocks(text);
        assert_eq!(extracted.closed_blocks.len(), 0); // Does not match multi-line closed_block_re
        assert!(extracted.unclosed_block_at_end.is_none()); // Ends with ```, so not unclosed
    }

    #[test]
    fn test_extract_block_no_keyword_newline_then_closing_ticks() {
        let text = "```\n```"; // No keyword, newline, then immediately closing ticks
        let extracted = extract_raw_code_blocks(text);
        assert_eq!(extracted.closed_blocks.len(), 0); // Does not match multi-line closed_block_re
        assert!(extracted.unclosed_block_at_end.is_none()); // Ends with ```, so not unclosed
    }

    #[test]
    fn test_extract_block_with_trailing_text_after_closing_backticks() {
        let text = "```rust\nlet x = 1;\n```<|END_RESPONSE|>";
        let extracted = extract_raw_code_blocks(text);
        // New regex will match the block part. The trailing text means it's not cleanly on its own line.
        // The regex `$` ensures the closing ``` is at the end of its line (possibly with spaces).
        // So, this should NOT match as a closed block if trailing text is on the same line as ```.
        assert_eq!(
            extracted.closed_blocks.len(),
            0,
            "Block with same-line trailing text should not be closed_blocks"
        );
        // It should be caught by unclosed_block_re if it's the last thing.
        assert!(
            extracted.unclosed_block_at_end.is_some(),
            "Expected unclosed block due to trailing text"
        );
        let unclosed = extracted.unclosed_block_at_end.unwrap();
        assert_eq!(unclosed.keyword, "rust");
        assert_eq!(
            unclosed.content_after_keyword,
            "let x = 1;\n```<|END_RESPONSE|>" // parse_keyword_and_content_for_unclosed behavior
        );
    }

    #[test]
    fn test_extract_block_with_trailing_text_and_spaces_after_closing_backticks() {
        let text = "```python\nprint('hello')\n```  some trailing text";
        let extracted = extract_raw_code_blocks(text);
        assert_eq!(
            extracted.closed_blocks.len(),
            0,
            "Block with same-line trailing text should not be closed_blocks"
        );
        assert!(
            extracted.unclosed_block_at_end.is_some(),
            "Expected unclosed block due to trailing text"
        );
        let unclosed = extracted.unclosed_block_at_end.unwrap();
        assert_eq!(unclosed.keyword, "python");
        assert_eq!(
            unclosed.content_after_keyword,
            "print('hello')\n```  some trailing text" // parse_keyword_and_content_for_unclosed behavior
        );
    }

    #[test]
    fn test_extract_block_no_keyword_with_trailing_text() {
        let text = "```\ncontent\n```trailing";
        let extracted = extract_raw_code_blocks(text);
        assert_eq!(
            extracted.closed_blocks.len(),
            0,
            "Block with same-line trailing text should not be closed_blocks"
        );
        assert!(
            extracted.unclosed_block_at_end.is_some(),
            "Expected unclosed block due to trailing text"
        );
        let unclosed = extracted.unclosed_block_at_end.unwrap();
        assert_eq!(unclosed.keyword, "");
        assert_eq!(unclosed.content_after_keyword, "\ncontent\n```trailing");
    }

    // New tests for unclosed block detection
    #[test]
    fn test_extract_unclosed_block_at_end() {
        let text = "```replace\nsome code\nand more code"; // No closing ```
        let extracted = extract_raw_code_blocks(text);
        assert!(extracted.closed_blocks.is_empty());
        assert!(extracted.unclosed_block_at_end.is_some());
        let unclosed = extracted.unclosed_block_at_end.unwrap();
        assert_eq!(unclosed.keyword, "replace");
        assert_eq!(unclosed.content_after_keyword, "some code\nand more code");
        assert_eq!(
            unclosed.full_block_text,
            "```replace\nsome code\nand more code"
        );
    }

    #[test]
    fn test_extract_closed_block_then_unclosed_block() {
        let text =
            "```summary\nThis is closed.\n```\nSome text between.\n```replace\nThis is not closed.";
        let extracted = extract_raw_code_blocks(text);
        assert_eq!(extracted.closed_blocks.len(), 1);
        assert_eq!(extracted.closed_blocks[0].keyword, "summary");
        assert_eq!(
            extracted.closed_blocks[0].content_after_keyword,
            "This is closed."
        ); // Adjusted

        assert!(extracted.unclosed_block_at_end.is_some());
        let unclosed = extracted.unclosed_block_at_end.unwrap();
        assert_eq!(unclosed.keyword, "replace");
        // content_after_keyword for unclosed block includes leading newline if present after keyword
        assert_eq!(unclosed.content_after_keyword, "This is not closed.");
        // full_block_text for unclosed block starts from its ```
        assert_eq!(unclosed.full_block_text, "```replace\nThis is not closed.");
    }

    #[test]
    fn test_extract_unclosed_block_no_keyword() {
        let text = "```\nUnclosed content without keyword";
        let extracted = extract_raw_code_blocks(text);
        assert!(extracted.closed_blocks.is_empty());
        assert!(extracted.unclosed_block_at_end.is_some());
        let unclosed = extracted.unclosed_block_at_end.unwrap();
        assert_eq!(unclosed.keyword, ""); // Keyword is empty
        assert_eq!(
            unclosed.content_after_keyword,
            "\nUnclosed content without keyword"
        ); // Content includes leading newline
        assert_eq!(
            unclosed.full_block_text,
            "```\nUnclosed content without keyword"
        );
    }

    #[test]
    fn test_extract_only_opening_backticks() {
        let text = "```";
        let extracted = extract_raw_code_blocks(text);
        assert!(extracted.closed_blocks.is_empty());
        assert!(extracted.unclosed_block_at_end.is_some());
        let unclosed = extracted.unclosed_block_at_end.unwrap();
        assert_eq!(unclosed.keyword, "");
        assert_eq!(unclosed.content_after_keyword, "");
        assert_eq!(unclosed.full_block_text, "```");
    }

    #[test]
    fn test_extract_opening_backticks_with_keyword_only() {
        let text = "```rust";
        let extracted = extract_raw_code_blocks(text);
        assert!(extracted.closed_blocks.is_empty());
        assert!(extracted.unclosed_block_at_end.is_some());
        let unclosed = extracted.unclosed_block_at_end.unwrap();
        assert_eq!(unclosed.keyword, "rust");
        assert_eq!(unclosed.content_after_keyword, "");
        assert_eq!(unclosed.full_block_text, "```rust");
    }

    #[test]
    fn test_text_after_unclosed_block_is_part_of_it() {
        let text = "```replace\ncontent\nmore content after newline";
        let extracted = extract_raw_code_blocks(text);
        assert!(extracted.closed_blocks.is_empty());
        let unclosed = extracted
            .unclosed_block_at_end
            .expect("Should find an unclosed block");
        assert_eq!(unclosed.keyword, "replace");
        assert_eq!(
            unclosed.content_after_keyword,
            "content\nmore content after newline"
        );
    }

    #[test]
    fn test_no_false_positive_unclosed_if_text_after_closed_is_not_block() {
        let text = "```closed\ncontent\n```\nThis is just trailing text.";
        let extracted = extract_raw_code_blocks(text);
        assert_eq!(extracted.closed_blocks.len(), 1);
        assert!(extracted.unclosed_block_at_end.is_none());
    }

    #[test]
    fn test_extract_back_to_back_blocks_newline_separated() {
        let text = "```range-replace\npath1\n1-1\n---\ncontent1\n```\n```range-replace\npath2\n2-2\n---\ncontent2\n```";
        let extracted = extract_raw_code_blocks(text);
        assert_eq!(extracted.closed_blocks.len(), 2);
        assert!(extracted.unclosed_block_at_end.is_none());

        assert_eq!(extracted.closed_blocks[0].keyword, "range-replace");
        assert_eq!(
            extracted.closed_blocks[0].content_after_keyword,
            "path1\n1-1\n---\ncontent1"
        );
        assert_eq!(
            extracted.closed_blocks[0].full_block_text,
            "```range-replace\npath1\n1-1\n---\ncontent1\n```"
        );

        assert_eq!(extracted.closed_blocks[1].keyword, "range-replace");
        assert_eq!(
            extracted.closed_blocks[1].content_after_keyword,
            "path2\n2-2\n---\ncontent2"
        );
        assert_eq!(
            extracted.closed_blocks[1].full_block_text,
            "```range-replace\npath2\n2-2\n---\ncontent2\n```"
        );
    }

    #[test]
    fn test_extract_block_with_internal_triple_backticks_followed_by_another_block() {
        let text = "```rust\nfn main() {\n    let s = \"```text\\nThis is not a real block end.\\n```\";\n    println!(\"{}\", s);\n}\n```\n```summary\nSecond block summary.\n```";
        let extracted = extract_raw_code_blocks(text);

        assert_eq!(
            extracted.closed_blocks.len(),
            2,
            "Expected 2 closed blocks, found {}. Full text: '{}'",
            extracted.closed_blocks.len(),
            text
        );
        assert!(
            extracted.unclosed_block_at_end.is_none(),
            "Expected no unclosed block"
        );

        // Verify first block (rust code)
        assert_eq!(extracted.closed_blocks[0].keyword, "rust");
        let expected_rust_content =
            "fn main() {\n    let s = \"```text\\nThis is not a real block end.\\n```\";\n    println!(\"{}\", s);\n}";
        assert_eq!(
            extracted.closed_blocks[0].content_after_keyword,
            expected_rust_content
        );
        let expected_rust_full_block = format!("```rust\n{}\n```", expected_rust_content);
        assert_eq!(
            extracted.closed_blocks[0].full_block_text,
            expected_rust_full_block
        );

        // Verify second block (summary)
        assert_eq!(extracted.closed_blocks[1].keyword, "summary");
        assert_eq!(
            extracted.closed_blocks[1].content_after_keyword,
            "Second block summary."
        );
        assert_eq!(
            extracted.closed_blocks[1].full_block_text,
            "```summary\nSecond block summary.\n```"
        );
    }

    #[test]
    fn test_extract_block_content_ends_same_line_as_closing_ticks_not_allowed_by_current_regex() {
        // This test verifies that the current regex, which expects content to end with a newline
        // before the closing ``` line, does NOT match blocks formatted differently.
        let text_no_newline_before_closing_ticks = "```rust\nlet x = 1;```"; // Content and closing ``` on same line after keyword line
        let extracted = extract_raw_code_blocks(text_no_newline_before_closing_ticks);
        assert_eq!(
            extracted.closed_blocks.len(),
            0,
            "Block where content ends on same line as closing ``` should not match current closed_block_re"
        );
        // Depending on unclosed logic, it might be caught as unclosed or ignored.
        // Current unclosed logic might see it as a malformed closed block if it ends with ```.
    }

    #[cfg(test)]
    mod tests_strip_think_tags {
        use super::strip_think_tags;

        #[test]
        fn test_strip_no_tags() {
            let text = "This is a normal sentence.";
            assert_eq!(strip_think_tags(text), "This is a normal sentence.");
        }

        #[test]
        fn test_strip_one_think_block() {
            let text = "Before <think>this is thinking</think> After.";
            assert_eq!(strip_think_tags(text), "Before  After.");
        }

        #[test]
        fn test_strip_multiple_think_blocks() {
            let text = "<think>thought1</think> Visible <think>thought2</think> AlsoVisible <think>thought3</think>";
            assert_eq!(strip_think_tags(text), " Visible  AlsoVisible ");
        }

        #[test]
        fn test_strip_think_block_with_newlines() {
            let text = "Line 1\n<think>multi\nline\nthought</think>\nLine 2";
            assert_eq!(strip_think_tags(text), "Line 1\n\nLine 2");
        }

        #[test]
        fn test_strip_think_block_at_start() {
            let text = "<think>planning...</think>Actual content";
            assert_eq!(strip_think_tags(text), "Actual content");
        }

        #[test]
        fn test_strip_think_block_at_end() {
            let text = "Actual content<think>...done thinking</think>";
            assert_eq!(strip_think_tags(text), "Actual content");
        }

        #[test]
        fn test_strip_empty_think_block() {
            let text = "Content <think></think> More content";
            assert_eq!(strip_think_tags(text), "Content  More content");
        }

        #[test]
        fn test_strip_malformed_only_opening_tag() {
            let text = "Text <think> still thinking but no closing tag";
            assert_eq!(
                strip_think_tags(text),
                "Text <think> still thinking but no closing tag"
            );
        }

        #[test]
        fn test_strip_malformed_only_closing_tag() {
            let text = "Text no opening tag </think> More text";
            assert_eq!(
                strip_think_tags(text),
                "Text no opening tag </think> More text"
            );
        }

        #[test]
        fn test_strip_think_block_containing_code_block_syntax() {
            let text = "Before <think>```special\ncontent\n```</think> After.";
            assert_eq!(strip_think_tags(text), "Before  After.");
        }

        #[test]
        fn test_strip_adjacent_think_blocks() {
            let text = "A<think>t1</think><think>t2</think>B";
            assert_eq!(strip_think_tags(text), "AB");
        }

        #[test]
        fn test_strip_think_tags_with_leading_trailing_whitespace_in_tags() {
            // The regex is specific to <think> and </think> without internal spaces.
            // If tags could be <think > or < /think >, the regex would need adjustment.
            // Current regex will not match these.
            let text = "Before <think> thinking </think> After.";
            assert_eq!(strip_think_tags(text), "Before  After.");

            let text_malformed_tags = "Before <think > thinking < /think > After.";
            assert_eq!(
                strip_think_tags(text_malformed_tags),
                "Before <think > thinking < /think > After."
            );
        }

        #[test]
        fn test_strip_think_with_answer_tags_case1() {
            // Test case 1: Empty think block followed by answer block
            let text = " <think>  </think> <answer> To calculate $52 \\times 55$, you can break it down like this:  $$ 52 \\times\n               (50 + 5) $$  Now, apply the distributive property:  $$ 52 \\times 50 = 2600 \\quad \\text{and} \\quad 52 \\times 5 = 260 $$\n               Add the two results together:  $$ 2600 + 260 = 2860 $$  So, $52 \\times 55 = 2860$. </answer>";
            let expected = "  <answer> To calculate $52 \\times 55$, you can break it down like this:  $$ 52 \\times\n               (50 + 5) $$  Now, apply the distributive property:  $$ 52 \\times 50 = 2600 \\quad \\text{and} \\quad 52 \\times 5 = 260 $$\n               Add the two results together:  $$ 2600 + 260 = 2860 $$  So, $52 \\times 55 = 2860$. </answer>";
            assert_eq!(strip_think_tags(text), expected);
        }

        #[test]
        fn test_strip_think_with_answer_tags_case2() {
            // Test case 2: Think block with content followed by answer block
            let text = " <think> To calculate $52 \\times 55$, you can break it down like this:  $$ 52 \\times 55 = 52 \\times\n               (50 + 5) $$  Now, apply the distributive property:  $$ 52 \\times 50 = 2600 \\quad \\text{and} \\quad 52 \\times 5 = 260 $$\n               Add the two results together:  $$ 2600 + 260 = 2860 $$   </think> <answer>  So, $52 \\times 55 = 2860$. </answer>";
            let expected = "  <answer>  So, $52 \\times 55 = 2860$. </answer>";
            assert_eq!(strip_think_tags(text), expected);
        }

        #[test]
        fn test_strip_think_preserves_answer_tags() {
            // Ensure answer tags are preserved when think tags are stripped
            let text = "Before <think>internal thought</think> <answer>final answer</answer> After";
            let expected = "Before  <answer>final answer</answer> After";
            assert_eq!(strip_think_tags(text), expected);
        }

        #[test]
        fn test_strip_think_with_whitespace_only() {
            // Test think block with only whitespace
            let text = "<think>   \n  \t  </think>Content after";
            let expected = "Content after";
            assert_eq!(strip_think_tags(text), expected);
        }
    }
}

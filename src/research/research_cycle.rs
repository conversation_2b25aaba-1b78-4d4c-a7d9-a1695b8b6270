// src/research/research_cycle.rs
use crate::config::AppConfig;
use crate::editor::ProcessingSignal; // Added ProcessingSignal import
use crate::files::file_handler::LabeledFile;
use crate::llm::{ChatMessage, ChatRole, LLMClient, MessageType};
use log::trace; // Added trace import
use log::{debug, error, info, warn}; // Added error back
use regex::Regex;
use std::collections::HashMap;
use std::env;
use std::path::PathBuf;
use std::process::Command as StdCommand; // Renamed to avoid conflict
use std::sync::Arc; // Added import
use tokio::sync::mpsc; // Added mpsc import // For std::env::current_dir()

// Added imports
use crate::block_parsing::bash_command_block::BashCommandBlockParser;
use crate::block_parsing::complete_files_block::CompleteFilesBlockParser;
use crate::block_parsing::processor::{
    process_llm_response_with_blocks, BlockCount, BlockExpectation,
};
use crate::block_parsing::traits::ParsableBlock;
use crate::notifications::execute_notification_command;
use crate::notifications::truncate_with_ellipsis;
use crate::research::prompts::{
    construct_initial_research_prompt, construct_research_follow_up_prompt,
};

// Helper function (adapted from BashCommandBlockParser)
fn local_split_command_respecting_quotes(command: &str) -> Vec<String> {
    let mut segments = Vec::new();
    if command.is_empty() {
        return segments;
    }

    let mut current_segment_start_index = 0;
    let mut in_single_quote = false;
    let mut in_double_quote = false;
    let mut last_char_was_escape = false;

    for (i, char_code) in command.char_indices() {
        match char_code {
            '\\' if !last_char_was_escape => {
                last_char_was_escape = true;
                continue;
            }
            '\'' if !in_double_quote && !last_char_was_escape => {
                in_single_quote = !in_single_quote;
            }
            '"' if !in_single_quote && !last_char_was_escape => {
                in_double_quote = !in_double_quote;
            }
            '|' if !in_single_quote && !in_double_quote && !last_char_was_escape => {
                segments.push(command[current_segment_start_index..i].to_string());
                current_segment_start_index = i + 1;
            }
            _ => {}
        }
        last_char_was_escape = false;
    }
    segments.push(command[current_segment_start_index..].to_string());
    segments
}

// Helper function to check command availability
fn check_command_availability(command_name: &str) -> bool {
    match StdCommand::new(command_name).arg("--version").output() {
        Ok(output) => output.status.success(),
        Err(_) => false,
    }
}

// Helper function to convert grep to rg if rg is available
// Returns the potentially modified command string and a boolean indicating if conversion happened.
fn convert_grep_to_rg_if_needed(original_command_str: &str) -> (String, bool) {
    if !check_command_availability("rg") {
        return (original_command_str.to_string(), false);
    }

    let original_trimmed = original_command_str.trim();
    if original_trimmed.is_empty() {
        return (original_command_str.to_string(), false);
    }

    let segments = local_split_command_respecting_quotes(original_trimmed);
    let mut processed_segments = Vec::new();
    let mut any_segment_modified = false;

    if segments.is_empty() {
        // Should not happen if original_trimmed is not empty, but as a safeguard.
        return (original_command_str.to_string(), false);
    }

    for segment_str in segments {
        let mut current_segment = segment_str.trim().to_string();
        let initial_segment_state_for_comparison = current_segment.clone();

        // Check if this segment is a grep command (starts with "grep" followed by space or end of string)
        if Regex::new(r"^\s*grep(\s|$)")
            .unwrap()
            .is_match(&current_segment)
        {
            // 1. Replace "grep" with "rg"
            current_segment = Regex::new(r"^\s*grep\b")
                .unwrap()
                .replace(&current_segment, "rg")
                .to_string();

            // 2. Convert --include=PATTERN or --include PATTERN to -g PATTERN
            let include_regex = Regex::new(r#"--include(?:=|\s+)('[^']+'|"[^"]+"|\S+)"#).unwrap();
            current_segment = include_regex
                .replace_all(&current_segment, r"-g $1")
                .to_string();

            // 3. Remove common flags that are default or handled differently in rg.
            // Each regex targets the flag preceded by a space to ensure it's a distinct option.
            current_segment = Regex::new(r"\s-r\b")
                .unwrap()
                .replace_all(&current_segment, "")
                .to_string();
            current_segment = Regex::new(r"\s-R\b")
                .unwrap()
                .replace_all(&current_segment, "")
                .to_string();
            current_segment = Regex::new(r"\s--recursive\b")
                .unwrap()
                .replace_all(&current_segment, "")
                .to_string();
            current_segment = Regex::new(r"\s-n\b")
                .unwrap()
                .replace_all(&current_segment, "")
                .to_string();
            current_segment = Regex::new(r"\s-H\b")
                .unwrap()
                .replace_all(&current_segment, "")
                .to_string();
            current_segment = Regex::new(r"\s--colou?r(?:=\w*)?\b")
                .unwrap()
                .replace_all(&current_segment, "")
                .to_string();
            current_segment = Regex::new(r"\s-E\b")
                .unwrap()
                .replace_all(&current_segment, "")
                .to_string();

            // 4. Cleanup spaces within the modified segment
            current_segment = Regex::new(r"\s+")
                .unwrap()
                .replace_all(&current_segment, " ")
                .trim()
                .to_string();

            if current_segment != initial_segment_state_for_comparison {
                any_segment_modified = true;
            }
        }
        processed_segments.push(current_segment);
    }

    if !any_segment_modified {
        // If no grep segment was actually modified, return the original string
        // to preserve original formatting if no functional change was made.
        return (original_command_str.to_string(), false);
    }

    let final_command = processed_segments.join(" | ");
    // The `any_segment_modified` flag already correctly indicates if a conversion happened.
    (final_command, any_segment_modified)
}

pub const MAX_RESEARCH_TURNS: usize = 250; // Max iterations for the research loop

// Removed ParsedLLMAction enum
// Removed parse_llm_response function

pub async fn run_research_cycle(
    app_config: &AppConfig,
    llm_client: Arc<dyn LLMClient>, // Changed to Arc
    task_description: &str,
    // initial_files_message_content: &str, // Removed
    cycle_messages: &mut Vec<ChatMessage>,
    historical_context: &[ChatMessage], // History *before* this research task
    operation_signal_tx: Option<&mpsc::Sender<ProcessingSignal>>, // Changed to Option<&Sender>
    task_id: &str,
    current_path_for_commands: &PathBuf, // New parameter
    files_already_in_context: &HashMap<PathBuf, LabeledFile>, // New parameter
) -> Result<crate::research::types::CategorizedFilePaths, String> {
    // Returns CategorizedFilePaths or error string
    // Notification for research started
    let notification_title = "🔍 LLEdit Research Started".to_string();
    let notification_message =
        truncate_with_ellipsis(&format!("Task: \"{}\"", task_description), 100);
    execute_notification_command(
        &app_config.notification_command,
        current_path_for_commands,
        &notification_title,
        &notification_message,
    );
    debug!("🔍 Research Started\n");

    // 1. Initial Prompt Construction
    let bash_parser = BashCommandBlockParser;
    let complete_parser = CompleteFilesBlockParser;

    let initial_prompt_content = construct_initial_research_prompt(
        app_config,
        task_description,
        files_already_in_context, // Pass the map directly
        &bash_parser,
        &complete_parser,
    );

    cycle_messages.push(ChatMessage {
        role: ChatRole::User,
        content: initial_prompt_content.clone(),
        message_type: MessageType::Text,
    });

    // 2. LLM Interaction Loop
    let bash_parser_instance = BashCommandBlockParser;
    let complete_parser_instance = CompleteFilesBlockParser; // Instance for calling methods

    let expectations = vec![
        BlockExpectation {
            parser: Box::new(BashCommandBlockParser), // New instance for trait object
            expected_count: BlockCount::Optional,     // Expect 0 or 1 bash command
        },
        BlockExpectation {
            parser: Box::new(CompleteFilesBlockParser), // New instance for trait object
            expected_count: BlockCount::Optional,       // Expect 0 or 1 complete-files block
        },
    ];

    for turn in 0..MAX_RESEARCH_TURNS {
        trace!("Research Turn {}", turn + 1);

        let messages_to_send: Vec<ChatMessage> = historical_context
            .iter()
            .cloned()
            .chain(cycle_messages.iter().cloned()) // `cycle_messages` has the evolving conversation of this research task
            .collect();

        // Note: Removed full prompt trace logging to reduce verbosity - prompts are largely the same each turn

        match llm_client.chat(&messages_to_send).await {
            // llm_client is already Arc, can be passed directly
            Ok(llm_response) => {
                let response_content = llm_response.clone();
                trace!(
                    "[Task {}] Raw LLM Response (Turn {}):\n---\n{}\n---",
                    task_id,
                    turn + 1,
                    response_content
                );
                let assistant_message_this_turn = ChatMessage {
                    role: ChatRole::Assistant,
                    content: response_content.clone(), // The raw response from LLM
                    message_type: MessageType::Text,
                };
                // Accumulate messages for this turn before sending signal
                let mut messages_generated_this_turn = vec![assistant_message_this_turn.clone()];
                cycle_messages.push(assistant_message_this_turn);

                // Process LLM response using block processing framework
                // process_llm_response_with_blocks will add its own retry prompts and responses to `cycle_messages`.
                // No need for temp_conversation_log_for_block_processing.

                match process_llm_response_with_blocks(
                    &response_content,
                    &expectations,
                    task_description, // original_user_prompt for context in retries
                    &[],              // available_files (empty for research context)
                    llm_client.clone(), // Clone Arc
                    historical_context, // This is the prefix
                    cycle_messages,   // Pass the main cycle_messages for retries to be appended
                    app_config,
                )
                .await
                {
                    Ok(processed_output) => {
                        let mut action_taken_this_turn = false;

                        // Priority 1: CompleteFilesBlock
                        if let Some(block) =
                            processed_output.get_first_block_by_id(&complete_parser_instance.id())
                        {
                            action_taken_this_turn = true;
                            match complete_parser_instance.parse_to_file_list(block) {
                                Ok(parsed_paths) => {
                                    let mut valid_resolved_paths = Vec::new();
                                    let mut invalid_original_paths_for_prompt = Vec::new();
                                    let cwd = env::current_dir().unwrap_or_else(|_| {
                                        warn!("Could not get current working directory for research path validation. Using '.' as fallback.");
                                        PathBuf::from(".")
                                    });

                                    for original_path in parsed_paths {
                                        let resolved_path = if original_path.is_absolute() {
                                            original_path.clone()
                                        } else {
                                            cwd.join(&original_path)
                                        };

                                        let canonical_path_attempt =
                                            tokio::fs::canonicalize(&resolved_path).await;

                                        if canonical_path_attempt.is_ok()
                                            && tokio::fs::metadata(
                                                &canonical_path_attempt.as_ref().unwrap(),
                                            )
                                            .await
                                            .is_ok()
                                        {
                                            if tokio::fs::metadata(
                                                canonical_path_attempt.as_ref().unwrap(),
                                            )
                                            .await
                                            .map_or(false, |m| m.is_file())
                                            {
                                                valid_resolved_paths
                                                    .push(canonical_path_attempt.unwrap());
                                            } else {
                                                invalid_original_paths_for_prompt.push(format!(
                                                    "{} (is a directory, not a file)",
                                                    original_path.display()
                                                ));
                                            }
                                        } else {
                                            invalid_original_paths_for_prompt
                                                .push(original_path.display().to_string());
                                        }
                                    }

                                    if !invalid_original_paths_for_prompt.is_empty() {
                                        let invalid_paths_str =
                                            invalid_original_paths_for_prompt.join(", ");
                                        let system_error_msg_content = format!("The `complete-files` block contained invalid or non-existent file paths: [{}].", invalid_paths_str);
                                        let system_error_msg = ChatMessage {
                                            role: ChatRole::System,
                                            content: system_error_msg_content,
                                            message_type: MessageType::Text,
                                        };
                                        messages_generated_this_turn.push(system_error_msg.clone());
                                        cycle_messages.push(system_error_msg);

                                        let user_reprompt_content = format!(
                                            "Your previous `complete-files` block listed some file paths that were not found or were invalid (e.g., directories instead of files): [{}]. \
                                            Please review these paths and provide a corrected `complete-files` block. \
                                            Ensure all paths are either absolute or relative to the current working directory ('{}') and point to actual files.",
                                            invalid_paths_str,
                                            cwd.display()
                                        );
                                        let user_reprompt_msg = ChatMessage {
                                            role: ChatRole::User,
                                            content: user_reprompt_content,
                                            message_type: MessageType::Text,
                                        };
                                        messages_generated_this_turn
                                            .push(user_reprompt_msg.clone());
                                        cycle_messages.push(user_reprompt_msg);
                                    } else {
                                        // Check if LLM provided any valid paths before consuming valid_resolved_paths
                                        let was_llm_response_empty_of_valid_paths =
                                            valid_resolved_paths.is_empty();

                                        // Filter out paths that are already in context
                                        // Clone valid_resolved_paths before into_iter if it's needed later.
                                        // It is needed later for construct_categorize_files_prompt.
                                        let truly_new_discovered_paths: Vec<PathBuf> =
                                            valid_resolved_paths
                                                .clone()
                                                .into_iter()
                                                .filter(|p| {
                                                    !files_already_in_context.contains_key(p)
                                                })
                                                .collect();

                                        if truly_new_discovered_paths.is_empty() {
                                            debug!("{}", "-".repeat(70));
                                            info!("Research Complete!");
                                            // Use the stored boolean for the condition below
                                            if !was_llm_response_empty_of_valid_paths
                                                && truly_new_discovered_paths.is_empty()
                                            {
                                                info!("LLM identified relevant files, but all were already in context. No new files added.");
                                            } else {
                                                info!("LLM did not identify any new relevant files to add to context.");
                                            }
                                            debug!("{}", "-".repeat(70));

                                            let notification_title =
                                                "🔍 LLEdit Research Finished".to_string();
                                            let notification_message =
                                                "No new files added to context.".to_string();
                                            execute_notification_command(
                                                &app_config.notification_command,
                                                current_path_for_commands,
                                                &notification_title,
                                                &notification_message,
                                            );

                                            if let Some(tx) = operation_signal_tx {
                                                if tx
                                                    .send(ProcessingSignal::ResearchTurnComplete {
                                                        task_id: task_id.to_string(),
                                                        turn_number: turn + 1,
                                                        messages_this_turn:
                                                            messages_generated_this_turn.clone(),
                                                    })
                                                    .await
                                                    .is_err()
                                                {
                                                    warn!("Failed to send final research turn complete signal (no new files) for task_id '{}'.", task_id);
                                                }
                                            }
                                            return Ok(crate::research::types::CategorizedFilePaths::default());
                                        // Return empty categorized paths
                                        } else {
                                            // Categorization Step
                                            let context_parser = crate::block_parsing::context_files_block::ContextFilesBlockParser;
                                            let edit_parser = crate::block_parsing::edit_files_block::EditFilesBlockParser;
                                            let categorization_prompt = crate::research::prompts::construct_categorize_files_prompt(
                                                app_config,
                                                task_description,
                                                &valid_resolved_paths, // Use all valid paths found by complete-files before filtering
                                                &context_parser,
                                                &edit_parser,
                                            );

                                            let mut categorization_dialog =
                                                historical_context.to_vec(); // Start with global history
                                            categorization_dialog
                                                .extend(cycle_messages.iter().cloned()); // Add current research task's history
                                            categorization_dialog.push(ChatMessage {
                                                role: ChatRole::User,
                                                content: categorization_prompt.clone(),
                                                message_type: MessageType::Text,
                                            });

                                            trace!("[Task {}] Categorization prompt sent to LLM:\n---\n{}\n---", task_id, categorization_prompt);

                                            match llm_client.chat(&categorization_dialog).await {
                                                // llm_client is already Arc
                                                Ok(categorization_response_text) => {
                                                    trace!("[Task {}] Raw LLM Response for Categorization:\n---\n{}\n---", task_id, categorization_response_text);
                                                    categorization_dialog.push(ChatMessage {
                                                        role: ChatRole::Assistant,
                                                        content: categorization_response_text
                                                            .clone(),
                                                        message_type: MessageType::Text,
                                                    });

                                                    let categorization_expectations = vec![
                                                        BlockExpectation {
                                                            parser: Box::new(context_parser),
                                                            expected_count: BlockCount::Optional,
                                                        },
                                                        BlockExpectation {
                                                            parser: Box::new(edit_parser),
                                                            expected_count: BlockCount::Optional,
                                                        },
                                                    ];

                                                    // Use a temporary conversation log for this specific block processing,
                                                    // starting with the categorization prompt and its response.
                                                    // The history_prefix for block processing should be the global historical_context.
                                                    // The conversation_log_for_block_processing should be the research task's history + this categorization attempt.
                                                    let mut
                                                    conversation_log_for_categorization_block_processing =
                                                        cycle_messages.clone(); // History of current research task
                                                    conversation_log_for_categorization_block_processing.push(ChatMessage {
                                                        role: ChatRole::User,
                                                        content: categorization_prompt.clone(),
                                                        message_type: MessageType::Text,
                                                    });
                                                    conversation_log_for_categorization_block_processing.push(ChatMessage {
                                                        role: ChatRole::Assistant,
                                                        content: categorization_response_text.clone(),
                                                        message_type: MessageType::Text,
                                                    });

                                                    match process_llm_response_with_blocks(
                                                        &categorization_response_text,
                                                        &categorization_expectations,
                                                        &categorization_prompt, // The prompt that led to this response
                                                        &[], // No available_files needed for these parsers
                                                        llm_client.clone(), // Clone Arc
                                                        historical_context, // Use global history as the prefix
                                                        &mut conversation_log_for_categorization_block_processing, // Pass the combined log
                                                        app_config,
                                                    )
                                                    .await
                                                    {
                                                        Ok(categorization_processed_output) => {
                                                            let context_files_raw_unfiltered = categorization_processed_output
                                                                .get_first_block_by_id(&crate::block_parsing::context_files_block::ContextFilesBlockParser.id())
                                                                .and_then(|block| crate::block_parsing::context_files_block::ContextFilesBlockParser.parse_to_file_list(block).ok())
                                                                .unwrap_or_default();

                                                            let edit_files_raw_unfiltered = categorization_processed_output
                                                                .get_first_block_by_id(&crate::block_parsing::edit_files_block::EditFilesBlockParser.id())
                                                                .and_then(|block| crate::block_parsing::edit_files_block::EditFilesBlockParser.parse_to_file_list(block).ok())
                                                                .unwrap_or_default();

                                                            // Filter out paths containing "`" which are likely parsing artifacts from LLM response
                                                            let context_files_raw =
                                                                context_files_raw_unfiltered
                                                                    .into_iter()
                                                                    .filter(|p| {
                                                                        !p.to_string_lossy()
                                                                            .contains("`")
                                                                    })
                                                                    .collect::<Vec<_>>();

                                                            let edit_files_raw =
                                                                edit_files_raw_unfiltered
                                                                    .into_iter()
                                                                    .filter(|p| {
                                                                        !p.to_string_lossy()
                                                                            .contains("`")
                                                                    })
                                                                    .collect::<Vec<_>>();

                                                            // Validate and canonicalize categorized paths, ensuring they are subsets of valid_resolved_paths
                                                            let mut final_context_files =
                                                                Vec::new();
                                                            let mut final_edit_files = Vec::new();
                                                            let valid_resolved_paths_set: std::collections::HashSet<_> = valid_resolved_paths.iter().cloned().collect();

                                                            for path_raw in context_files_raw {
                                                                let resolved =
                                                                    if path_raw.is_absolute() {
                                                                        path_raw
                                                                    } else {
                                                                        cwd.join(&path_raw)
                                                                    };
                                                                if let Ok(canon) =
                                                                    tokio::fs::canonicalize(
                                                                        &resolved,
                                                                    )
                                                                    .await
                                                                {
                                                                    if valid_resolved_paths_set
                                                                        .contains(&canon)
                                                                    {
                                                                        final_context_files
                                                                            .push(canon);
                                                                    } else {
                                                                        warn!("Categorization: Context file {} not in original discovered list or invalid.", resolved.display());
                                                                    }
                                                                } else {
                                                                    warn!("Categorization: Context file {} could not be canonicalized.", resolved.display());
                                                                }
                                                            }
                                                            for path_raw in edit_files_raw {
                                                                let resolved =
                                                                    if path_raw.is_absolute() {
                                                                        path_raw
                                                                    } else {
                                                                        cwd.join(&path_raw)
                                                                    };
                                                                if let Ok(canon) =
                                                                    tokio::fs::canonicalize(
                                                                        &resolved,
                                                                    )
                                                                    .await
                                                                {
                                                                    if valid_resolved_paths_set
                                                                        .contains(&canon)
                                                                    {
                                                                        final_edit_files
                                                                            .push(canon);
                                                                    } else {
                                                                        warn!("Categorization: Edit file {} not in original discovered list or invalid.", resolved.display());
                                                                    }
                                                                } else {
                                                                    warn!("Categorization: Edit file {} could not be canonicalized.", resolved.display());
                                                                }
                                                            }
                                                            final_context_files.sort();
                                                            final_context_files.dedup();
                                                            final_edit_files.sort();
                                                            final_edit_files.dedup();

                                                            let categorized = crate::research::types::CategorizedFilePaths {
                                                                all_discovered: valid_resolved_paths.clone(), // All valid paths before filtering by context
                                                                context_files: final_context_files,
                                                                edit_files: final_edit_files,
                                                            };

                                                            // Filter out paths already in context from the categorized lists
                                                            let truly_new_categorized_paths = crate::research::types::CategorizedFilePaths {
                                                                all_discovered: truly_new_discovered_paths.clone(), // This was already filtered
                                                                context_files: categorized.context_files.into_iter().filter(|p| !files_already_in_context.contains_key(p)).collect(),
                                                                edit_files: categorized.edit_files.into_iter().filter(|p| !files_already_in_context.contains_key(p)).collect(),
                                                            };

                                                            // Logging and notification for successful research with categorization
                                                            debug!("{}", "-".repeat(70));
                                                            info!("Research Complete!");
                                                            info!("LLM found {} new relevant files ({} for context, {} for editing).", 
                                                                  truly_new_categorized_paths.all_discovered.len(),
                                                                  truly_new_categorized_paths.context_files.len(),
                                                                  truly_new_categorized_paths.edit_files.len());
                                                            let current_dir_log = env::current_dir(
                                                            )
                                                            .unwrap_or_else(|_| PathBuf::from("."));
                                                            let mut new_files_list_display_log =
                                                                Vec::new();
                                                            for path in &truly_new_categorized_paths
                                                                .all_discovered
                                                            {
                                                                let display_path_log = path
                                                                    .strip_prefix(&current_dir_log)
                                                                    .unwrap_or(path)
                                                                    .display()
                                                                    .to_string();
                                                                info!("  - {}", display_path_log);
                                                                new_files_list_display_log
                                                                    .push(display_path_log);
                                                            }
                                                            debug!("{}", "-".repeat(70));
                                                            let notification_title_log =
                                                                "🔍 LLEdit Research Finished"
                                                                    .to_string();
                                                            let notification_message_log =
                                                                truncate_with_ellipsis(
                                                                    &format!(
                                                                        "New files added: {}",
                                                                        new_files_list_display_log
                                                                            .join(", ")
                                                                    ),
                                                                    200,
                                                                );
                                                            execute_notification_command(
                                                                &app_config.notification_command,
                                                                current_path_for_commands,
                                                                &notification_title_log,
                                                                &notification_message_log,
                                                            );

                                                            if let Some(tx) = operation_signal_tx {
                                                                if tx.send(ProcessingSignal::ResearchTurnComplete {
                                                                    task_id: task_id.to_string(),
                                                                    turn_number: turn + 1, // This is the turn where complete-files was found
                                                                    messages_this_turn: messages_generated_this_turn.clone(), // Includes the complete-files block and its processing
                                                                }).await.is_err() {
                                                                    warn!("Failed to send final research turn complete signal for task_id '{}'.", task_id);
                                                                }
                                                            }
                                                            return Ok(truly_new_categorized_paths);
                                                        }
                                                        Err(e) => {
                                                            warn!("Failed to process LLM response for file categorization: {}. Proceeding with all discovered files uncategorized.", e);
                                                            // Fallback: return all discovered files without specific categorization
                                                            let fallback_categorized = crate::research::types::CategorizedFilePaths {
                                                                all_discovered: truly_new_discovered_paths.clone(),
                                                                context_files: Vec::new(), // Empty as categorization failed
                                                                edit_files: Vec::new(),    // Empty as categorization failed
                                                            };
                                                            return Ok(fallback_categorized);
                                                        }
                                                    }
                                                }
                                                Err(e) => {
                                                    // Error from LLM call for categorization
                                                    warn!("LLM call for file categorization failed: {}. Proceeding with all discovered files uncategorized.", e);
                                                    let fallback_categorized = crate::research::types::CategorizedFilePaths {
                                                        all_discovered: truly_new_discovered_paths.clone(),
                                                        context_files: Vec::new(),
                                                        edit_files: Vec::new(),
                                                    };
                                                    return Ok(fallback_categorized);
                                                }
                                            }
                                        }
                                    }
                                }
                                Err(e) => {
                                    let err_msg = format!("Error parsing complete-files block structure: {}. LLM response was: '{}'", e, response_content);
                                    error!("{}", err_msg);
                                    let system_msg = ChatMessage {
                                        role: ChatRole::System,
                                        content: err_msg,
                                        message_type: MessageType::Text,
                                    };
                                    messages_generated_this_turn.push(system_msg.clone());
                                    cycle_messages.push(system_msg);
                                }
                            }
                        }
                        // Priority 2: BashCommandBlock (only if CompleteFilesBlock was not found and processed)
                        else if let Some(block) =
                            processed_output.get_first_block_by_id(&bash_parser_instance.id())
                        {
                            action_taken_this_turn = true;
                            match bash_parser_instance.parse_to_string(block) {
                                Ok(raw_command_str) => {
                                    let (final_command_to_run, _was_converted) =
                                        convert_grep_to_rg_if_needed(&raw_command_str);
                                    let current_dir_to_run_in = current_path_for_commands.clone();
                                    debug!(
                                        "[{}] > {}",
                                        current_dir_to_run_in.display(),
                                        final_command_to_run
                                    );

                                    let command_str_for_spawn = final_command_to_run.clone();
                                    let command_str_for_error_log = final_command_to_run.clone();
                                    let current_dir_for_spawn = current_dir_to_run_in.clone();

                                    let output_result = tokio::task::spawn_blocking(move || {
                                        StdCommand::new("sh")
                                            .arg("-c")
                                            .arg(&command_str_for_spawn)
                                            .current_dir(&current_dir_for_spawn)
                                            .output()
                                    })
                                    .await;

                                    let (execution_summary_for_llm, command_output_for_trace_log) =
                                        match output_result {
                                            Ok(Ok(output)) => {
                                                let stdout_str =
                                                    String::from_utf8_lossy(&output.stdout);
                                                let stderr_str =
                                                    String::from_utf8_lossy(&output.stderr);
                                                let status_code_str =
                                                    output.status.code().map_or_else(
                                                        || "Signal".to_string(),
                                                        |c| c.to_string(),
                                                    );
                                                let stdout_trimmed_for_llm = stdout_str.trim();
                                                let stderr_trimmed_for_llm = stderr_str.trim();
                                                let result_block_content_for_llm =
                                                    if stdout_trimmed_for_llm.is_empty()
                                                        && stderr_trimmed_for_llm.is_empty()
                                                    {
                                                        "No output to STDOUT or STDERR.".to_string()
                                                    } else {
                                                        format!("--- STDOUT ---\n{}\n--- STDERR ---\n{}", stdout_trimmed_for_llm, stderr_trimmed_for_llm)
                                                    };
                                                let summary_for_llm = format!(
                                                "The command `{}` was executed in the directory `{}`.\nExit Status: {}\n```result\n{}\n```",
                                                final_command_to_run,
                                                current_dir_to_run_in.display(),
                                                status_code_str,
                                                result_block_content_for_llm
                                            );
                                                let mut log_stdout_parts: Vec<String> =
                                                    stdout_str.lines().map(String::from).collect();
                                                let mut log_stderr_parts: Vec<String> =
                                                    stderr_str.lines().map(String::from).collect();
                                                if log_stdout_parts.len() > 5 {
                                                    let further_stdout_count =
                                                        log_stdout_parts.len() - 5;
                                                    log_stdout_parts.truncate(5);
                                                    log_stdout_parts.push(format!(
                                                        "+{} Further Lines",
                                                        further_stdout_count
                                                    ));
                                                }
                                                if log_stderr_parts.len() > 5 {
                                                    let further_stderr_count =
                                                        log_stderr_parts.len() - 5;
                                                    log_stderr_parts.truncate(5);
                                                    log_stderr_parts.push(format!(
                                                        "+{} Further Lines",
                                                        further_stderr_count
                                                    ));
                                                }
                                                let trace_log_stdout =
                                                    log_stdout_parts.join("\n").trim().to_string();
                                                let trace_log_stderr =
                                                    log_stderr_parts.join("\n").trim().to_string();
                                                let trace_log_output = format!(
                                                    "[{}] Output:\n{}\n{}",
                                                    current_dir_to_run_in.display(),
                                                    trace_log_stdout,
                                                    trace_log_stderr
                                                );
                                                (summary_for_llm, trace_log_output)
                                            }
                                            Ok(Err(e)) => {
                                                let error_msg = format!("[Task {}] Failed to execute command '{}' in directory '{}': {}", task_id, command_str_for_error_log, current_dir_to_run_in.display(), e);
                                                error!("{}", error_msg);
                                                (error_msg.clone(), error_msg)
                                            }
                                            Err(e) => {
                                                let error_msg = format!("[Task {}] Failed to spawn command execution task for '{}': {}", task_id, command_str_for_error_log, e);
                                                error!("{}", error_msg);
                                                (error_msg.clone(), error_msg)
                                            }
                                        };
                                    trace!("{}", command_output_for_trace_log);

                                    let system_msg = ChatMessage {
                                        role: ChatRole::System,
                                        content: execution_summary_for_llm,
                                        message_type: MessageType::Text,
                                    };
                                    messages_generated_this_turn.push(system_msg.clone());
                                    cycle_messages.push(system_msg);

                                    // Send signal that a bash command was executed
                                    if let Some(tx) = operation_signal_tx {
                                        if tx
                                            .send(ProcessingSignal::ResearchBashCommandExecuted)
                                            .await
                                            .is_err()
                                        {
                                            warn!("Failed to send ResearchBashCommandExecuted signal for task_id '{}'.", task_id);
                                            // Decide if this is critical enough to stop research
                                        }
                                    }

                                    let user_follow_up_message_content =
                                        construct_research_follow_up_prompt(app_config);
                                    let user_follow_up_message = ChatMessage {
                                        role: ChatRole::User,
                                        content: user_follow_up_message_content,
                                        message_type: MessageType::Text,
                                    };
                                    messages_generated_this_turn
                                        .push(user_follow_up_message.clone());
                                    cycle_messages.push(user_follow_up_message);
                                }
                                Err(e) => {
                                    let err_msg = format!("Critical error: Bash command block was validated but failed to parse to string: {}. LLM response was: '{}'", e, response_content);
                                    error!("{}", err_msg);
                                    let system_msg = ChatMessage {
                                        role: ChatRole::System,
                                        content: err_msg,
                                        message_type: MessageType::Text,
                                    };
                                    messages_generated_this_turn.push(system_msg.clone());
                                    cycle_messages.push(system_msg);
                                }
                            }
                        }

                        // Fallback: If neither a CompleteFilesBlock nor a BashCommandBlock was processed.
                        if !action_taken_this_turn {
                            // This implies that process_llm_response_with_blocks returned Ok,
                            // but the successfully_parsed_blocks did not contain a complete-files or bash block,
                            // or they were present but the logic above didn't fully process them (e.g. parse_to_string failed for bash).
                            // The original fallback condition also checked `processed_output.successfully_parsed_blocks.is_empty()`.
                            // If `action_taken_this_turn` is false, it means neither of the primary actions was taken.
                            debug!("LLM response (Turn {}, task_id '{}') did not result in a valid action (bash or complete-files). Content: '{}'", turn + 1, task_id, response_content);
                            let fallback_user_prompt = ChatMessage {
                                role: ChatRole::User,
                                content: "Your previous response did not contain a usable `bash` command or a `complete-files` block. Please try again. Your response MUST contain exactly one code block: either one `bash` block or one `complete-files` block. Do not provide multiple blocks or other text outside the single chosen block.".to_string(),
                                message_type: MessageType::Text,
                            };
                            messages_generated_this_turn.push(fallback_user_prompt.clone());
                            cycle_messages.push(fallback_user_prompt);
                        }
                    }
                    Err(e) => {
                        // Error from process_llm_response_with_blocks
                        let err_msg = format!("Error processing LLM response blocks (Turn {}, task_id '{}'): {}. LLM response: '{}'", turn + 1, task_id, e, response_content);
                        error!("{}", err_msg);
                        let system_msg = ChatMessage {
                            role: ChatRole::System,
                            content: err_msg.clone(),
                            message_type: MessageType::Text,
                        };
                        messages_generated_this_turn.push(system_msg.clone());
                        cycle_messages.push(system_msg);
                        // Add a generic user prompt to try and recover
                        let recovery_user_prompt = ChatMessage {
                            role: ChatRole::User,
                            content: "There was an error processing your last response. Please try again, ensuring you provide either a single `bash` command or single `complete-files` block.".to_string(),
                            message_type: MessageType::Text,
                        };
                        messages_generated_this_turn.push(recovery_user_prompt.clone());
                        cycle_messages.push(recovery_user_prompt);
                    }
                }
                // Send turn complete signal
                if let Some(tx) = operation_signal_tx {
                    if tx
                        .send(ProcessingSignal::ResearchTurnComplete {
                            task_id: task_id.to_string(),
                            turn_number: turn + 1,
                            messages_this_turn: messages_generated_this_turn,
                        })
                        .await
                        .is_err()
                    {
                        warn!("Failed to send research turn complete signal for task_id '{}'. Aborting research.", task_id);
                        return Err("Failed to send turn update to TUI.".to_string());
                    }
                }
            }
            Err(e) => {
                // Error from llm_client.chat()
                let err_msg = format!(
                    "LLM chat call failed (Turn {}, task_id '{}'): {}",
                    turn + 1,
                    task_id,
                    e
                );
                error!("{}", err_msg);
                // Don't push to cycle_messages as it might be redundant if TUI logs this error.
                // The main_process will receive this error and can inform TUI.
                return Err(err_msg);
            }
        }
    }

    // Loop finished due to MAX_RESEARCH_TURNS
    let max_turns_msg = format!(
        "Maximum research turns reached for task_id '{}' without completion.",
        task_id
    );
    warn!("{}", max_turns_msg);
    // Send final turn signal with this system message
    if let Some(tx) = operation_signal_tx {
        if tx
            .send(ProcessingSignal::ResearchTurnComplete {
                task_id: task_id.to_string(),
                turn_number: MAX_RESEARCH_TURNS,
                messages_this_turn: vec![ChatMessage {
                    role: ChatRole::System,
                    content: max_turns_msg.clone(),
                    message_type: MessageType::Text,
                }],
            })
            .await
            .is_err()
        {
            warn!(
                "Failed to send max turns research signal for task_id '{}'.",
                task_id
            );
        }
    }
    Err(max_turns_msg)
}

//! # Custom TUI Logger Implementation (High-Performance Rewrite)
//!
//! This module provides a complete replacement for the `tui-logger` crate with enhanced features
//! and a high-performance rendering engine designed for handling very long, wrapped log lines.
//!
//! ## Core Performance Enhancements
//!
//! The key to performance is a lazy, cached text wrapping system. Instead of re-wrapping text
//! on every frame, this implementation calculates line break positions once for a given width
//! and caches the result. Subsequent renders use this cache to perform near-instantaneous
//! string slicing, eliminating the primary bottleneck of the previous design. This makes
//! scrolling through logs with thousands of long, multi-line messages smooth and responsive.
//!
//! ## Features
//! - **High-Performance Rendering**: Advanced text wrapping with lazy caching.
//! - **Rich Interaction**: Cursor navigation and visual selection.
//! - **Dynamic Effects**: Typewriter animations for text.
//! - **Customization**: Custom styling and log levels.
//! - **Seamless Integration**: Drop-in replacement using the `log` crate.
//!
//! ## Usage
//!
//! Initialize the logger in your main application:
//! ```rust,ignore
//! let log_receiver = crate::tui::tui_logger::init();
//! ```
//!
//! Use standard logging macros throughout your application:
//! ```rust,ignore
//! log::info!("This will appear in the TUI");
//! log::error!("Error messages are styled in red");
//! ```
//!
//! Or use the enhanced macros for special effects:
//! ```rust,ignore
//! tui_typewriter_info!(100, "This text will appear with typewriter effect");
//! ```

use chrono::{DateTime, Local, Timelike};
use log::{Level, Log, Metadata, Record};
use once_cell::sync::OnceCell;
use ratatui::buffer::Buffer;
use ratatui::layout::Rect;
use ratatui::style::{Color, Style};
use ratatui::text::{Line, Span, Text};
use ratatui::widgets::{Block, Paragraph, StatefulWidget, Widget, Wrap};
use std::collections::VecDeque;
use std::time::Instant;
use tokio::sync::mpsc;

use crate::interactive::commands_utils::copy_to_clipboard;

// --- Data Structures for Log Messages ---

/// Represents the log level for a message.
#[derive(Clone, Debug)]
pub enum LogLevel {
    Trace,
    Debug,
    Info,
    Warn,
    Error,
    Custom(String),
}

impl From<Level> for LogLevel {
    fn from(level: Level) -> Self {
        match level {
            Level::Trace => LogLevel::Trace,
            Level::Debug => LogLevel::Debug,
            Level::Info => LogLevel::Info,
            Level::Warn => LogLevel::Warn,
            Level::Error => LogLevel::Error,
        }
    }
}

/// Configuration for typewriter effect animation.
#[derive(Clone, Debug)]
pub struct TypewriterEffect {
    pub enabled: bool,
    pub speed_millis: u64,
}

impl Default for TypewriterEffect {
    fn default() -> Self {
        Self { enabled: false, speed_millis: 60 }
    }
}

/// A single raw log message with its metadata.
#[derive(Clone, Debug)]
pub struct LogMessage {
    pub timestamp: DateTime<Local>,
    pub level: LogLevel,
    pub message: String,
    pub style: Option<Style>,
    pub typewriter: Option<TypewriterEffect>,
}

impl LogMessage {
    pub fn new(level: LogLevel, message: String) -> Self {
        Self { timestamp: Local::now(), level, message, style: None, typewriter: None }
    }
}

// --- High-Performance Caching Structures ---

/// Caches the calculated line breaks for a specific render width.
#[derive(Clone, Debug)]
struct CachedWrapping {
    width: usize,
    line_breaks: Vec<usize>,
}

/// A wrapper around `LogMessage` that handles the lazy caching of text wrapping.
#[derive(Clone, Debug)]
struct DisplayMessage {
    log: LogMessage,
    wrap_cache: Option<CachedWrapping>,
}

impl DisplayMessage {
    fn new(log: LogMessage) -> Self {
        Self { log, wrap_cache: None }
    }

    fn ensure_cache_updated(&mut self, width: usize) {
        if let Some(cache) = &self.wrap_cache {
            if cache.width == width {
                return;
            }
        }
        let breaks = compute_line_breaks(&self.log.message, width);
        self.wrap_cache = Some(CachedWrapping { width, line_breaks: breaks });
    }

    fn get_line_count(&mut self, width: usize) -> usize {
        if width == 0 { return 1; }
        self.ensure_cache_updated(width);
        self.wrap_cache.as_ref().unwrap().line_breaks.len()
    }

    fn get_line_slice(&mut self, line_index: usize, width: usize) -> &str {
        self.ensure_cache_updated(width);
        let breaks = &self.wrap_cache.as_ref().unwrap().line_breaks;
        if line_index >= breaks.len() { return ""; }
        let start = breaks[line_index];
        let end = if line_index + 1 < breaks.len() {
            breaks[line_index + 1]
        } else {
            self.log.message.len()
        };
        &self.log.message[start..end]
    }
}

/// Computes the starting byte indices of each line for a given string and width.
/// This is the corrected, high-performance implementation.
fn compute_line_breaks(text: &str, width: usize) -> Vec<usize> {
    if text.is_empty() || width == 0 {
        return vec![0];
    }

    let mut breaks = Vec::with_capacity(text.len() / width);
    breaks.push(0);

    let mut current_line_start_byte_idx = 0;
    let mut last_word_end_byte_idx = 0;
    let mut current_line_char_len = 0;

    for (current_char_byte_idx, c) in text.char_indices() {
        current_line_char_len += 1;

        if c.is_whitespace() {
            last_word_end_byte_idx = current_char_byte_idx + c.len_utf8();
        }

        if current_line_char_len > width {
            // Determine the correct position to break the line.
            let break_at = if last_word_end_byte_idx > current_line_start_byte_idx {
                // Break at the end of the last complete word.
                last_word_end_byte_idx
            } else {
                // The current word is longer than the line, so we must hard-break it.
                current_char_byte_idx
            };

            breaks.push(break_at);
            current_line_start_byte_idx = break_at;

            // **THE CRITICAL FIX IS HERE:**
            // Calculate the length of the new line, which consists of the text
            // that has spilled over from the previous line.
            let new_line_slice = &text[current_line_start_byte_idx..current_char_byte_idx + c.len_utf8()];
            current_line_char_len = new_line_slice.chars().count();
            
            // After a break, the last potential break point is the start of the new line.
            last_word_end_byte_idx = current_line_start_byte_idx;
        }
    }

    breaks
}


// --- State Management ---

pub struct TuiLoggerState {
    messages: VecDeque<DisplayMessage>,
    max_messages: usize,
    scroll_offset: usize,
    autoscroll_enabled: bool,
    last_view_height: usize,
    cursor_enabled: bool,
    cursor_visual_line: usize,
    visual_mode_enabled: bool,
    visual_selection_start_line: usize,
    visual_line_map: Vec<(usize, usize)>,
    total_visual_lines: usize,
    last_content_width: usize,
    trace_style: Style,
    debug_style: Style,
    info_style: Style,
    warn_style: Style,
    error_style: Style,
    default_typewriter: TypewriterEffect,
    typing_queue: VecDeque<usize>,
    current_typing_line: Option<(usize, usize, Instant)>,
    typewriter_speed_millis: u64,
}

impl Default for TuiLoggerState {
    fn default() -> Self { Self::new() }
}

impl TuiLoggerState {
    pub fn new() -> Self {
        Self {
            messages: VecDeque::new(),
            max_messages: 10000,
            scroll_offset: 0,
            autoscroll_enabled: true,
            last_view_height: 0,
            cursor_enabled: false,
            cursor_visual_line: 0,
            visual_mode_enabled: false,
            visual_selection_start_line: 0,
            visual_line_map: Vec::new(),
            total_visual_lines: 0,
            last_content_width: 0,
            trace_style: Style::default().fg(Color::DarkGray),
            debug_style: Style::default().fg(Color::Blue),
            info_style: Style::default().fg(Color::Green),
            warn_style: Style::default().fg(Color::Yellow),
            error_style: Style::default().fg(Color::Red),
            default_typewriter: TypewriterEffect::default(),
            typing_queue: VecDeque::new(),
            current_typing_line: None,
            typewriter_speed_millis: 40,
        }
    }

    pub fn add_message(&mut self, mut message: LogMessage) {
        message.message = message.message.replace('\n', " ");
        let should_type = message.typewriter.as_ref().map_or(self.default_typewriter.enabled, |t| t.enabled);
        self.messages.push_back(DisplayMessage::new(message));
        let message_index = self.messages.len() - 1;
        if should_type { self.typing_queue.push_back(message_index); }

        if self.messages.len() > self.max_messages {
            self.messages.pop_front();
            self.visual_line_map.clear();
            self.last_content_width = 0;
            for index in &mut self.typing_queue { *index = index.saturating_sub(1); }
            if let Some((ref mut current_index, _, _)) = self.current_typing_line {
                *current_index = current_index.saturating_sub(1);
            }
        }
        if self.autoscroll_enabled { self.scroll_offset = 0; }
    }

    fn rebuild_visual_line_map(&mut self, content_width: usize) {
        let message_width = content_width.saturating_sub(15);
        if message_width == 0 {
            self.total_visual_lines = self.messages.len();
            return;
        }
        let width_changed = self.last_content_width != content_width;
        if width_changed { self.visual_line_map.clear(); }
        let start_msg_idx = self.visual_line_map.last().map_or(0, |(idx, _)| idx + 1);

        for msg_idx in start_msg_idx..self.messages.len() {
            let line_count = self.messages[msg_idx].get_line_count(message_width);
            for line_in_msg in 0..line_count {
                self.visual_line_map.push((msg_idx, line_in_msg));
            }
        }
        self.total_visual_lines = self.visual_line_map.len();
        self.last_content_width = content_width;
    }

    // --- Interaction Methods ---

    pub fn scroll_up(&mut self, amount: usize) {
        let max_scroll = self.total_visual_lines.saturating_sub(self.last_view_height);
        self.scroll_offset = (self.scroll_offset + amount).min(max_scroll);
        self.autoscroll_enabled = false;
    }

    pub fn scroll_down(&mut self, amount: usize) {
        self.scroll_offset = self.scroll_offset.saturating_sub(amount);
        if self.scroll_offset == 0 { self.autoscroll_enabled = true; }
    }

    pub fn page_up(&mut self) { self.scroll_up(self.last_view_height.saturating_sub(1).max(1)); }
    pub fn page_down(&mut self) { self.scroll_down(self.last_view_height.saturating_sub(1).max(1)); }

    pub fn jump_to_top(&mut self) {
        let max_scroll = self.total_visual_lines.saturating_sub(self.last_view_height);
        self.scroll_offset = max_scroll;
        self.autoscroll_enabled = false;
    }

    pub fn jump_to_bottom(&mut self) {
        self.scroll_offset = 0;
        self.autoscroll_enabled = true;
    }

    pub fn clear_messages(&mut self) {
        self.messages.clear();
        self.visual_line_map.clear();
        self.total_visual_lines = 0;
        self.scroll_offset = 0;
        self.cursor_visual_line = 0;
        self.autoscroll_enabled = true;
        self.visual_mode_enabled = false;
    }

    pub fn enable_cursor_mode(&mut self) {
        self.cursor_enabled = true;
        self.autoscroll_enabled = false;
        if self.total_visual_lines > 0 {
            self.cursor_visual_line = self.total_visual_lines.saturating_sub(1);
            self.ensure_cursor_visible();
        }
    }

    pub fn disable_cursor_mode(&mut self) {
        self.cursor_enabled = false;
        self.visual_mode_enabled = false;
        if self.scroll_offset == 0 { self.autoscroll_enabled = true; }
    }

    pub fn cursor_up(&mut self) {
        if self.cursor_enabled {
            self.cursor_visual_line = self.cursor_visual_line.saturating_sub(1);
            self.ensure_cursor_visible();
        }
    }

    pub fn cursor_down(&mut self) {
        if self.cursor_enabled && self.total_visual_lines > 0 {
            let max_line = self.total_visual_lines.saturating_sub(1);
            self.cursor_visual_line = (self.cursor_visual_line + 1).min(max_line);
            self.ensure_cursor_visible();
        }
    }

    pub fn cursor_up_by(&mut self, lines: usize) {
        if self.cursor_enabled {
            self.cursor_visual_line = self.cursor_visual_line.saturating_sub(lines);
            self.ensure_cursor_visible();
        }
    }

    pub fn cursor_down_by(&mut self, lines: usize) {
        if self.cursor_enabled && self.total_visual_lines > 0 {
            let max_line = self.total_visual_lines.saturating_sub(1);
            self.cursor_visual_line = (self.cursor_visual_line + lines).min(max_line);
            self.ensure_cursor_visible();
        }
    }

    pub fn cursor_to_top(&mut self) {
        if self.cursor_enabled {
            self.cursor_visual_line = 0;
            self.ensure_cursor_visible();
        }
    }

    pub fn cursor_to_bottom(&mut self) {
        if self.cursor_enabled && self.total_visual_lines > 0 {
            self.cursor_visual_line = self.total_visual_lines - 1;
            self.ensure_cursor_visible();
        }
    }

    fn ensure_cursor_visible(&mut self) {
        if !self.cursor_enabled || self.total_visual_lines <= self.last_view_height {
            self.scroll_offset = 0;
            return;
        }
        let view_height = self.last_view_height;
        let visible_start = self.total_visual_lines.saturating_sub(view_height + self.scroll_offset);
        let visible_end = visible_start + view_height;
        if self.cursor_visual_line < visible_start {
            self.scroll_offset += visible_start - self.cursor_visual_line;
        } else if self.cursor_visual_line >= visible_end {
            self.scroll_offset = self.scroll_offset.saturating_sub(self.cursor_visual_line - visible_end + 1);
        }
        self.autoscroll_enabled = self.scroll_offset == 0;
    }

    pub fn enter_visual_mode(&mut self) {
        if self.cursor_enabled {
            self.visual_mode_enabled = true;
            self.visual_selection_start_line = self.cursor_visual_line;
        }
    }

    pub fn exit_visual_mode(&mut self) { self.visual_mode_enabled = false; }
    pub fn is_visual_mode_enabled(&self) -> bool { self.visual_mode_enabled }

    pub fn get_visual_selection(&self) -> Option<(usize, usize)> {
        if !self.visual_mode_enabled { return None; }
        let start = self.visual_selection_start_line.min(self.cursor_visual_line);
        let end = self.visual_selection_start_line.max(self.cursor_visual_line);
        Some((start, end))
    }

    pub fn copy_selection(&mut self) -> Option<String> {
        let (start_visual_line, end_visual_line) = self.get_visual_selection()?;
        let message_width = self.last_content_width.saturating_sub(15);
        if message_width == 0 { return None; }
        let mut result = String::new();
        let mut last_msg_idx = usize::MAX;

        for visual_line_idx in start_visual_line..=end_visual_line {
            if let Some(&(msg_idx, line_within_msg)) = self.visual_line_map.get(visual_line_idx) {
                let (level_clone, timestamp_copy) = {
                    let log = &self.messages[msg_idx].log;
                    (log.level.clone(), log.timestamp)
                };

                let line_text = self.messages[msg_idx].get_line_slice(line_within_msg, message_width);

                if msg_idx != last_msg_idx {
                    let level_str = match &level_clone {
                        LogLevel::Info => "INFO", LogLevel::Warn => "WARN", LogLevel::Error => "ERROR",
                        LogLevel::Debug => "DEBUG", LogLevel::Trace => "TRACE",
                        LogLevel::Custom(s) => s.as_str(),
                    };
                    result.push_str(&format!(
                        "{:02}:{:02}:{:02} {}\n",
                        timestamp_copy.hour(), timestamp_copy.minute(), timestamp_copy.second(),
                        line_text.trim_end()
                    ));
                    last_msg_idx = msg_idx;
                } else {
                    result.push_str(&format!("               {}\n", line_text.trim_end()));
                }
            }
        }

        if !result.is_empty() {
            copy_to_clipboard(result.clone());
            self.exit_visual_mode();
            Some(result)
        } else {
            None
        }
    }

    // --- Typewriter and Styling Methods ---

    pub fn update_typewriter(&mut self) -> bool {
        if self.current_typing_line.is_none() {
            if let Some(message_index) = self.typing_queue.pop_front() {
                if message_index < self.messages.len() {
                    let speed = self.messages[message_index].log.typewriter.as_ref().map_or(self.typewriter_speed_millis, |t| t.speed_millis);
                    self.current_typing_line = Some((message_index, 0, Instant::now()));
                    self.typewriter_speed_millis = speed;
                    return true;
                }
            }
        }
        if let Some((msg_idx, ref mut char_idx, ref mut last_update)) = self.current_typing_line {
            if last_update.elapsed().as_millis() as u64 >= self.typewriter_speed_millis {
                *char_idx += 1;
                *last_update = Instant::now();
                if let Some(message) = self.messages.get(msg_idx) {
                    if *char_idx >= message.log.message.chars().count() { self.current_typing_line = None; return false; }
                } else { self.current_typing_line = None; return false; }
                return true;
            }
        }
        false
    }

    pub fn get_typewriter_text(&self, message_index: usize) -> Option<String> {
        if let Some((typing_index, char_index, _)) = self.current_typing_line {
            if typing_index == message_index {
                if let Some(message) = self.messages.get(message_index) {
                    return Some(message.log.message.chars().take(char_index).collect());
                }
            }
        }
        None
    }

    pub fn is_message_typing(&self, message_index: usize) -> bool {
        self.current_typing_line.map_or(false, |(idx, _, _)| idx == message_index)
    }

    fn get_style_for_level(&self, level: &LogLevel) -> Style {
        match level {
            LogLevel::Trace => self.trace_style,
            LogLevel::Debug => self.debug_style,
            LogLevel::Info => self.info_style,
            LogLevel::Warn => self.warn_style,
            LogLevel::Error => self.error_style,
            LogLevel::Custom(_) => Style::default(),
        }
    }
}

// --- Widget Rendering ---

pub struct TuiLoggerWidget<'a> {
    block: Option<Block<'a>>,
}

impl<'a> TuiLoggerWidget<'a> {
    pub fn new() -> Self { Self { block: None } }
    pub fn block(mut self, block: Block<'a>) -> Self { self.block = Some(block); self }
}

impl<'a> Default for TuiLoggerWidget<'a> {
    fn default() -> Self { Self::new() }
}

impl<'a> StatefulWidget for TuiLoggerWidget<'a> {
    type State = TuiLoggerState;

    fn render(mut self, area: Rect, buf: &mut Buffer, state: &mut Self::State) {
        let content_area = self.block.take().map_or(area, |b| {
            let inner = b.inner(area); b.render(area, buf); inner
        });
        let content_height = content_area.height as usize;
        let content_width = content_area.width as usize;
        if content_height == 0 || content_width == 0 { return; }

        state.last_view_height = content_height;
        let text_area_width = content_width.saturating_sub(1);
        state.rebuild_visual_line_map(text_area_width);
        if state.total_visual_lines == 0 { return; }
        
        let start_line = state.total_visual_lines.saturating_sub(content_height + state.scroll_offset);
        let end_line = (start_line + content_height).min(state.total_visual_lines);
        let mut lines = Vec::with_capacity(end_line - start_line);
        let message_width = text_area_width.saturating_sub(15);
        let selection = state.get_visual_selection();

        for visual_line_idx in start_line..end_line {
            let (msg_idx, line_within_msg) = state.visual_line_map[visual_line_idx];

            // Skip rendering subsequent visual lines of a message that's currently being typed
            let is_typing = state.is_message_typing(msg_idx);
            if is_typing && line_within_msg > 0 {
                continue; // Don't render this line at all
            }

            let line_content = {
                if is_typing && line_within_msg == 0 {
                    // Show typewriter text on the first line of the message
                    format!("{}█", state.get_typewriter_text(msg_idx).unwrap_or_default())
                } else {
                    state.messages[msg_idx].get_line_slice(line_within_msg, message_width).to_string()
                }
            };

            let (timestamp, style_from_log, level_clone) = {
                let log = &state.messages[msg_idx].log;
                (log.timestamp, log.style, log.level.clone())
            };
            let is_cursor_line = state.cursor_enabled && visual_line_idx == state.cursor_visual_line;
            let is_selected = selection.map_or(false, |(s, e)| visual_line_idx >= s && visual_line_idx <= e);
            let is_visual_mode = state.is_visual_mode_enabled();

            let mut style = style_from_log.unwrap_or_else(|| state.get_style_for_level(&level_clone));
            let bg_color = if is_cursor_line { Some(Color::Rgb(25, 25, 25)) } 
                           else if is_selected { Some(Color::Rgb(50, 50, 50)) } 
                           else { None };
            if let Some(bg) = bg_color { style = style.bg(bg); }

            let mut line_spans = Vec::new();
            if line_within_msg == 0 {
                let cursor_char = if is_cursor_line { "> " } else { "  " };
                let cursor_color = if is_visual_mode { Color::Blue } else { Color::Yellow };
                line_spans.push(Span::styled(cursor_char, Style::default().fg(cursor_color).bg(bg_color.unwrap_or(Color::Reset))));
                let timestamp_str = format!("{:02}:{:02}:{:02}.{:03} ",
                    timestamp.hour(), timestamp.minute(), timestamp.second(), timestamp.nanosecond() / 1_000_000);
                line_spans.push(Span::styled(timestamp_str, Style::default().fg(Color::DarkGray).bg(bg_color.unwrap_or(Color::Reset))));
            } else {
                line_spans.push(Span::styled(" ".repeat(15), style));
            }
            line_spans.push(Span::styled(line_content, style));
            lines.push(Line::from(line_spans));
        }
        Paragraph::new(Text::from(lines)).wrap(Wrap { trim: false }).render(content_area, buf);
    }
}

// --- Global Logger and Initialization ---

pub static LOGGER_CHANNEL: OnceCell<mpsc::Sender<LogMessage>> = OnceCell::new();

pub fn init() -> mpsc::Receiver<LogMessage> {
    let (tx, rx) = mpsc::channel(256);
    LOGGER_CHANNEL.set(tx).expect("TUI Logger already initialized");
    let logger = TuiLogger::new();
    log::set_max_level(log::LevelFilter::Trace);
    log::set_boxed_logger(Box::new(logger)).expect("Failed to set logger");
    rx
}

struct TuiLogger;
impl TuiLogger { fn new() -> Self { Self } }

impl Log for TuiLogger {
    fn enabled(&self, metadata: &Metadata) -> bool {
        let crate_name = env!("CARGO_PKG_NAME").replace('-', "_");
        metadata.target().starts_with(&crate_name)
    }

    fn log(&self, record: &Record) {
        if self.enabled(record.metadata()) {
            if let Some(sender) = LOGGER_CHANNEL.get() {
                let log_message = LogMessage::new(record.level().into(), format!("{}", record.args()));
                let _ = sender.try_send(log_message);
            }
        }
    }
    fn flush(&self) {}
}

// --- Custom Logging Macros ---

#[macro_export]
macro_rules! tui_info { ($($arg:tt)*) => { log::info!($($arg)*); }; }
#[macro_export]
macro_rules! tui_debug { ($($arg:tt)*) => { log::debug!($($arg)*); }; }
#[macro_export]
macro_rules! tui_error { ($($arg:tt)*) => { log::error!($($arg)*); }; }
#[macro_export]
macro_rules! tui_warn { ($($arg:tt)*) => { log::warn!($($arg)*); }; }
#[macro_export]
macro_rules! tui_trace { ($($arg:tt)*) => { log::trace!($($arg)*); }; }

#[macro_export]
macro_rules! tui_typewriter {
    ($level:expr, $speed:expr, $($arg:tt)*) => {
        if let Some(sender) = $crate::tui::tui_logger::LOGGER_CHANNEL.get() {
            let msg = $crate::tui::tui_logger::LogMessage {
                timestamp: chrono::Local::now(),
                level: $level,
                message: format!($($arg)*),
                style: None,
                typewriter: Some($crate::tui::tui_logger::TypewriterEffect {
                    enabled: true,
                    speed_millis: $speed,
                }),
            };
            let _ = sender.try_send(msg);
        }
    };
}

#[macro_export]
macro_rules! tui_typewriter_info { ($speed:expr, $($arg:tt)*) => { $crate::tui_typewriter!($crate::tui::tui_logger::LogLevel::Info, $speed, $($arg)*); }; }
#[macro_export]
macro_rules! tui_typewriter_debug { ($speed:expr, $($arg:tt)*) => { $crate::tui_typewriter!($crate::tui::tui_logger::LogLevel::Debug, $speed, $($arg)*); }; }
#[macro_export]
macro_rules! tui_typewriter_error { ($speed:expr, $($arg:tt)*) => { $crate::tui_typewriter!($crate::tui::tui_logger::LogLevel::Error, $speed, $($arg)*); }; }
#[macro_export]
macro_rules! tui_typewriter_warn { ($speed:expr, $($arg:tt)*) => { $crate::tui_typewriter!($crate::tui::tui_logger::LogLevel::Warn, $speed, $($arg)*); }; }
#[macro_export]
macro_rules! tui_typewriter_trace { ($speed:expr, $($arg:tt)*) => { $crate::tui_typewriter!($crate::tui::tui_logger::LogLevel::Trace, $speed, $($arg)*); }; }

#[macro_export]
macro_rules! tui_styled {
    ($level:expr, $style:expr, $($arg:tt)*) => {
        if let Some(sender) = $crate::tui::tui_logger::LOGGER_CHANNEL.get() {
            let msg = $crate::tui::tui_logger::LogMessage {
                timestamp: chrono::Local::now(),
                level: $level,
                message: format!($($arg)*),
                style: Some($style),
                typewriter: None,
            };
            let _ = sender.try_send(msg);
        }
    };
}

#[macro_export]
macro_rules! tui_styled_typewriter {
    ($level:expr, $style:expr, $speed:expr, $($arg:tt)*) => {
        if let Some(sender) = $crate::tui::tui_logger::LOGGER_CHANNEL.get() {
            let msg = $crate::tui::tui_logger::LogMessage {
                timestamp: chrono::Local::now(),
                level: $level,
                message: format!($($arg)*),
                style: Some($style),
                typewriter: Some($crate::tui::tui_logger::TypewriterEffect {
                    enabled: true,
                    speed_millis: $speed,
                }),
            };
            let _ = sender.try_send(msg);
        }
    };
}

#[macro_export]
macro_rules! tui_custom {
    ($level_name:expr, $($arg:tt)*) => {
        if let Some(sender) = $crate::tui::tui_logger::LOGGER_CHANNEL.get() {
            let msg = $crate::tui::tui_logger::LogMessage {
                timestamp: chrono::Local::now(),
                level: $crate::tui::tui_logger::LogLevel::Custom($level_name.to_string()),
                message: format!($($arg)*),
                style: None,
                typewriter: None,
            };
            let _ = sender.try_send(msg);
        }
    };
}
